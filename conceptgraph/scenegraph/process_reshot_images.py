"""
Process reshot images from CG/llava_candidate folder using LLaVA for captioning.
This script is based on build_scenegraph_cfslam.py but specifically for reshot images.
"""

import gc
import gzip
import json
import os
import pickle as pkl
import time
from dataclasses import dataclass
from pathlib import Path
from types import SimpleNamespace
from typing import List, Literal, Union
from textwrap import wrap

import cv2
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

import numpy as np
import rich
import torch
import tyro
from PIL import Image
from tqdm import tqdm, trange
from transformers import logging as hf_logging

import open3d as o3d

# Import OpenAI API
import openai

# Disable gradient computation
torch.autograd.set_grad_enabled(False)
hf_logging.set_verbosity_error()

# Configure OpenAI API
openai.api_key = os.getenv("OPENAI_API_KEY")
openai.api_base = "https://api.chatanywhere.tech/v1"
openai.api_key = "sk-Y13OAVhbyy2TdRKfc8vSML6JJFQtabZc2dmAmx4azip4AXzT"


@dataclass
class ProgramArgs:
    mode: Literal[
        "extract-reshot-captions",
        "refine-reshot-captions",
    ]

    # Path to cache directory
    cachedir: str = "saved/room0"
    
    # Path to prompts file
    prompts_path: str = "prompts/gpt_prompts.json"

    # Path to map file
    mapfile: str = "saved/room0/map/scene_map_cfslam.pkl.gz"

    # Path to reshot images directory
    reshot_dir: str = "CG/llava_candidate"

    # Device to use
    device: str = "cuda:0"

    # List of object IDs to process (default: all objects)
    object_ids: Union[List[int], None] = None


def load_scene_map(args, scene_map):
    """
    Loads a scene map from a gzip-compressed pickle file.
    """
    with gzip.open(Path(args.mapfile), "rb") as f:
        loaded_data = pkl.load(f)
        
        # Check the type of the loaded data to decide how to proceed
        if isinstance(loaded_data, dict) and "objects" in loaded_data:
            scene_map.load_serializable(loaded_data["objects"])
        elif isinstance(loaded_data, list) or isinstance(loaded_data, dict):
            scene_map.load_serializable(loaded_data)
        else:
            raise ValueError("Unexpected data format in map file.")
        print(f"Loaded {len(scene_map)} objects")


def plot_images_with_captions(images, captions, confidences, object_id, savedir):
    """
    Plot the reshot images with their captions and save to a directory.
    """
    n = len(images)  # Number of images (should be 4 for top4)
    nrows = 2
    ncols = 2
    fig, axarr = plt.subplots(nrows, ncols, figsize=(12, 10), squeeze=False)
    
    for i in range(n):
        row, col = divmod(i, 2)
        ax = axarr[row][col]
        ax.imshow(images[i])
        
        title_text = f"Caption: {captions[i]}\nConfidence: {confidences[i]:.2f}"
        
        # Wrap the caption text
        wrapped_title = '\n'.join(wrap(title_text, 30))
        
        ax.set_title(wrapped_title, fontsize=12)
        ax.axis('off')
    
    plt.tight_layout()
    plt.savefig(savedir / f"{object_id}.png")
    plt.close()


def save_json_to_file(json_str, filename):
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(json_str, f, indent=4, sort_keys=False)


def extract_reshot_captions(args):
    """
    Extract captions for reshot images using LLaVA.
    """
    from conceptgraph.llava.llava_model import LLaVaChat
    from conceptgraph.slam.slam_classes import MapObjectList

    # Load the scene map
    scene_map = MapObjectList()
    load_scene_map(args, scene_map)
    
    # Creating a namespace object to pass args to the LLaVA chat object
    chat_args = SimpleNamespace()
    chat_args.model_path = os.getenv("LLAVA_CKPT_PATH")
    chat_args.conv_mode = "v0_mmtag"  # "multimodal"
    chat_args.num_gpus = 1

    # Rich console for pretty printing
    console = rich.console.Console()

    # Initialize LLaVA chat
    chat = LLaVaChat(chat_args.model_path, chat_args.conv_mode, chat_args.num_gpus)
    print("LLaVA chat initialized...")
    query = "Describe the central object in the image."

    # Directories to save features and captions
    savedir_feat = Path(args.cachedir) / "reshot_feat_llava"
    savedir_feat.mkdir(exist_ok=True, parents=True)
    savedir_captions = Path(args.cachedir) / "reshot_captions_llava"
    savedir_captions.mkdir(exist_ok=True, parents=True)
    savedir_debug = Path(args.cachedir) / "reshot_captions_llava_debug"
    savedir_debug.mkdir(exist_ok=True, parents=True)

    caption_dict_list = []
    
    # Get the list of object IDs to process
    object_ids = args.object_ids if args.object_ids is not None else range(len(scene_map))
    
    # Get the reshot directory
    reshot_dir = Path(args.reshot_dir)
    
    for object_id in tqdm(object_ids, desc="Processing objects"):
        # Check if reshot images exist for this object
        reshot_images = [
            reshot_dir / f"{object_id}_top{i}.png" 
            for i in range(1, 5)  # top1 to top4
        ]
        
        # Filter out images that don't exist
        reshot_images = [img_path for img_path in reshot_images if img_path.exists()]
        
        if not reshot_images:
            print(f"No reshot images found for object {object_id}, skipping...")
            continue
            
        features = []
        captions = []
        confidences = []
        
        image_list = []
        caption_list = []
        confidences_list = []
        
        for img_path in reshot_images:
            # Load the image
            image = Image.open(img_path).convert("RGB")
            
            # Preprocess the image for LLaVA
            image_tensor = chat.image_processor.preprocess(image, return_tensors="pt")["pixel_values"][0]
            
            # Encode the image
            image_features = chat.encode_image(image_tensor[None, ...].half().cuda())
            features.append(image_features.detach().cpu())
            
            # Get caption from LLaVA
            chat.reset()
            console.print("[bold red]User:[/bold red] " + query)
            outputs = chat(query=query, image_features=image_features)
            console.print("[bold green]LLaVA:[/bold green] " + outputs)
            captions.append(outputs)
            
            # Store for visualization
            confidence = 1.0  # No confidence score for reshot images, using 1.0 as default
            image_list.append(image)
            caption_list.append(outputs)
            confidences_list.append(confidence)
        
        # Add to caption dictionary
        caption_dict_list.append({
            "id": object_id,
            "captions": captions,
        })
        
        # Concatenate the features
        if len(features) > 0:
            features = torch.cat(features, dim=0)
            
        # Save the feature descriptors
        torch.save(features, savedir_feat / f"{object_id}.pt")
        
        # Save debug visualization
        if len(image_list) > 0:
            plot_images_with_captions(
                image_list, caption_list, confidences_list, object_id, savedir_debug
            )
    
    # Remove the "The central object in the image is " prefix from captions
    for item in caption_dict_list:
        item["captions"] = [caption.replace("The central object in the image is ", "") for caption in item["captions"]]
    
    # Save the captions to a json file
    with open(Path(args.cachedir) / "reshot_llava_captions.json", "w", encoding="utf-8") as f:
        json.dump(caption_dict_list, f, indent=4, sort_keys=False)


def refine_reshot_captions(args):
    """
    Refine captions for reshot images using GPT-4.
    """
    from conceptgraph.slam.slam_classes import MapObjectList
    from conceptgraph.scenegraph.GPTPrompt import GPTPrompt

    # Load the captions for each segment
    caption_file = Path(args.cachedir) / "reshot_llava_captions.json"
    with open(caption_file, "r") as f:
        captions = json.load(f)
    
    # Load the scene map
    scene_map = MapObjectList()
    load_scene_map(args, scene_map)
    
    # Load the prompt
    gpt_messages = GPTPrompt().get_json()

    TIMEOUT = 25  # Timeout in seconds

    responses_savedir = Path(args.cachedir) / "reshot_gpt-4_responses"
    responses_savedir.mkdir(exist_ok=True, parents=True)

    responses = []
    unsuccessful_responses = 0

    # Loop over every object
    for caption_item in trange(len(captions), desc="Refining captions with GPT-4"):
        _caption = captions[caption_item]
        object_id = _caption["id"]
        
        # Prepare the object prompt
        _dict = {}
        _dict["id"] = object_id
        _dict["captions"] = _caption["captions"]
        
        # Make and format the full prompt
        preds = json.dumps(_dict, indent=0)

        start_time = time.time()
    
        curr_chat_messages = gpt_messages[:]
        curr_chat_messages.append({"role": "user", "content": preds})
        
        try:
            chat_completion = openai.ChatCompletion.create(
                model="gpt-4",
                messages=curr_chat_messages,
                timeout=TIMEOUT,
            )
            
            elapsed_time = time.time() - start_time
            if elapsed_time > TIMEOUT:
                print("Timeout exceeded!")
                _dict["response"] = "FAIL"
                save_json_to_file(_dict, responses_savedir / f"{object_id}.json")
                responses.append(json.dumps(_dict))
                unsuccessful_responses += 1
                continue
            
            # Count unsuccessful responses
            if "invalid" in chat_completion["choices"][0]["message"]["content"].strip("\n"):
                unsuccessful_responses += 1
                
            # Print output
            print(f"Object ID: {object_id}")
            print(chat_completion["choices"][0]["message"]["content"])
            print(f"Unsuccessful responses so far: {unsuccessful_responses}")
            
            _dict["response"] = chat_completion["choices"][0]["message"]["content"].strip("\n")
            
            # Save the response
            responses.append(json.dumps(_dict))
            save_json_to_file(_dict, responses_savedir / f"{object_id}.json")
            
        except Exception as e:
            print(f"Error processing object {object_id}: {e}")
            _dict["response"] = f"ERROR: {str(e)}"
            save_json_to_file(_dict, responses_savedir / f"{object_id}.json")
            responses.append(json.dumps(_dict))
            unsuccessful_responses += 1

    # Save all responses to a pickle file
    with open(Path(args.cachedir) / "reshot_gpt-4_responses.pkl", "wb") as f:
        pkl.dump(responses, f)


def main():
    # Process command-line args
    args = tyro.cli(ProgramArgs)
    
    if args.mode == "extract-reshot-captions":
        extract_reshot_captions(args)
    elif args.mode == "refine-reshot-captions":
        refine_reshot_captions(args)
    else:
        raise ValueError(f"Unknown mode: {args.mode}")


if __name__ == "__main__":
    main()
