"""
Process reshot images from CG/llava_candidate folder using LLaVA for captioning.
This script is based on build_scenegraph_cfslam.py but specifically for reshot images.
"""

import gc
import gzip
import json
import os
import pickle as pkl
import time
from dataclasses import dataclass
from pathlib import Path
from types import SimpleNamespace
from typing import List, Literal, Union
from textwrap import wrap

import cv2
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

import numpy as np
import rich
import torch
import tyro
from PIL import Image
from tqdm import tqdm, trange
from transformers import logging as hf_logging

import open3d as o3d

# Import OpenAI API
import openai

# Disable gradient computation
torch.autograd.set_grad_enabled(False)
hf_logging.set_verbosity_error()

# Configure OpenAI API
openai.api_key = os.getenv("OPENAI_API_KEY")
openai.api_base = "https://api.chatanywhere.tech/v1"
openai.api_key = "sk-Y13OAVhbyy2TdRKfc8vSML6JJFQtabZc2dmAmx4azip4AXzT"


@dataclass
class ProgramArgs:
    mode: Literal[
        "extract-reshot-captions",
        "refine-reshot-captions",
        "visualize-reshot-captions",
    ]

    # Path to cache directory
    cachedir: str = "saved/room0"

    # Path to prompts file
    prompts_path: str = "prompts/gpt_prompts.json"

    # Path to map file
    mapfile: str = "saved/room0/map/scene_map_cfslam.pkl.gz"

    # Path to reshot images directory
    reshot_dir: str = "CG/llava_candidate"

    # Device to use
    device: str = "cuda:0"

    # List of object IDs to process (default: all objects)
    object_ids: Union[List[int], None] = None

    # Output directory for visualizations
    vis_output_dir: str = "reshot_visualizations"


def load_scene_map(args, scene_map):
    """
    Loads a scene map from a gzip-compressed pickle file.
    """
    with gzip.open(Path(args.mapfile), "rb") as f:
        loaded_data = pkl.load(f)
        
        # Check the type of the loaded data to decide how to proceed
        if isinstance(loaded_data, dict) and "objects" in loaded_data:
            scene_map.load_serializable(loaded_data["objects"])
        elif isinstance(loaded_data, list) or isinstance(loaded_data, dict):
            scene_map.load_serializable(loaded_data)
        else:
            raise ValueError("Unexpected data format in map file.")
        print(f"Loaded {len(scene_map)} objects")


def plot_images_with_captions(images, captions, confidences, object_id, savedir):
    """
    Plot the reshot images with their captions and save to a directory.
    """
    n = len(images)  # Number of images (should be 4 for top4)
    nrows = 2
    ncols = 2
    _, axarr = plt.subplots(nrows, ncols, figsize=(12, 10), squeeze=False)

    for i in range(n):
        row, col = divmod(i, 2)
        ax = axarr[row][col]
        ax.imshow(images[i])

        title_text = f"Caption: {captions[i]}\nConfidence: {confidences[i]:.2f}"

        # Wrap the caption text
        wrapped_title = '\n'.join(wrap(title_text, 30))

        ax.set_title(wrapped_title, fontsize=12)
        ax.axis('off')

    plt.tight_layout()
    plt.savefig(savedir / f"{object_id}.png")
    plt.close()


def save_json_to_file(json_str, filename):
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(json_str, f, indent=4, sort_keys=False)


def extract_reshot_captions(args):
    """
    Extract captions for reshot images using LLaVA.
    """
    from conceptgraph.llava.llava_model import LLaVaChat
    from conceptgraph.slam.slam_classes import MapObjectList

    # Load the scene map
    scene_map = MapObjectList()
    load_scene_map(args, scene_map)
    
    # Creating a namespace object to pass args to the LLaVA chat object
    chat_args = SimpleNamespace()
    chat_args.model_path = os.getenv("LLAVA_CKPT_PATH")
    chat_args.conv_mode = "v0_mmtag"  # "multimodal"
    chat_args.num_gpus = 1

    # Rich console for pretty printing
    console = rich.console.Console()

    # Initialize LLaVA chat
    chat = LLaVaChat(chat_args.model_path, chat_args.conv_mode, chat_args.num_gpus)
    print("LLaVA chat initialized...")
    query = "The picture shows a point cloud of an object. What is this object?"

    # Directories to save features and captions
    savedir_feat = Path(args.cachedir) / "reshot_feat_llava"
    savedir_feat.mkdir(exist_ok=True, parents=True)
    savedir_captions = Path(args.cachedir) / "reshot_captions_llava"
    savedir_captions.mkdir(exist_ok=True, parents=True)
    savedir_debug = Path(args.cachedir) / "reshot_captions_llava_debug"
    savedir_debug.mkdir(exist_ok=True, parents=True)

    caption_dict_list = []
    
    # Get the list of object IDs to process
    object_ids = args.object_ids if args.object_ids is not None else range(len(scene_map))
    
    # Get the reshot directory
    reshot_dir = Path(args.reshot_dir)
    
    for object_id in tqdm(object_ids, desc="Processing objects"):
        # Check if reshot images exist for this object
        reshot_images = [
            reshot_dir / f"{object_id}_top{i}.png" 
            for i in range(1, 5)  # top1 to top4
        ]
        
        # Filter out images that don't exist
        reshot_images = [img_path for img_path in reshot_images if img_path.exists()]
        
        if not reshot_images:
            print(f"No reshot images found for object {object_id}, skipping...")
            continue
            
        features = []
        captions = []

        image_list = []
        caption_list = []
        confidences_list = []
        
        for img_path in reshot_images:
            # Load the image
            image = Image.open(img_path).convert("RGB")
            
            # Preprocess the image for LLaVA
            image_tensor = chat.image_processor.preprocess(image, return_tensors="pt")["pixel_values"][0]
            
            # Encode the image
            image_features = chat.encode_image(image_tensor[None, ...].half().cuda())
            features.append(image_features.detach().cpu())
            
            # Get caption from LLaVA
            chat.reset()
            console.print("[bold red]User:[/bold red] " + query)
            outputs = chat(query=query, image_features=image_features)
            console.print("[bold green]LLaVA:[/bold green] " + outputs)
            captions.append(outputs)
            
            # Store for visualization
            confidence = 1.0  # No confidence score for reshot images, using 1.0 as default
            image_list.append(image)
            caption_list.append(outputs)
            confidences_list.append(confidence)
        
        # Add to caption dictionary
        caption_dict_list.append({
            "id": object_id,
            "captions": captions,
        })
        
        # Concatenate the features
        if len(features) > 0:
            features = torch.cat(features, dim=0)
            
        # Save the feature descriptors
        torch.save(features, savedir_feat / f"{object_id}.pt")
        
        # Save debug visualization
        if len(image_list) > 0:
            plot_images_with_captions(
                image_list, caption_list, confidences_list, object_id, savedir_debug
            )
    
    # Remove common prefixes from captions to make them more concise
    common_prefixes = [
        "The central object in the image is ",
        "The object in the point cloud is ",
        "The object is ",
        "This is ",
        "I can see "
    ]

    for item in caption_dict_list:
        cleaned_captions = []
        for caption in item["captions"]:
            # Try to remove any of the common prefixes
            for prefix in common_prefixes:
                if caption.startswith(prefix):
                    caption = caption[len(prefix):]
                    break
            cleaned_captions.append(caption)
        item["captions"] = cleaned_captions
    
    # Save the captions to a json file
    with open(Path(args.cachedir) / "reshot_llava_captions.json", "w", encoding="utf-8") as f:
        json.dump(caption_dict_list, f, indent=4, sort_keys=False)


def refine_reshot_captions(args):
    """
    Refine captions for reshot images using GPT-4.
    """
    from conceptgraph.slam.slam_classes import MapObjectList
    from conceptgraph.scenegraph.GPTPrompt import GPTPrompt

    # Load the captions for each segment
    caption_file = Path(args.cachedir) / "reshot_llava_captions.json"
    with open(caption_file, "r") as f:
        captions = json.load(f)
    
    # Load the scene map
    scene_map = MapObjectList()
    load_scene_map(args, scene_map)
    
    # Load the prompt
    gpt_messages = GPTPrompt().get_json()

    TIMEOUT = 25  # Timeout in seconds

    responses_savedir = Path(args.cachedir) / "reshot_gpt-4_responses"
    responses_savedir.mkdir(exist_ok=True, parents=True)

    responses = []
    unsuccessful_responses = 0

    # Loop over every object
    for caption_item in trange(len(captions), desc="Refining captions with GPT-4"):
        _caption = captions[caption_item]
        object_id = _caption["id"]
        
        # Prepare the object prompt
        _dict = {}
        _dict["id"] = object_id
        _dict["captions"] = _caption["captions"]
        
        # Make and format the full prompt
        preds = json.dumps(_dict, indent=0)

        start_time = time.time()
    
        curr_chat_messages = gpt_messages[:]
        curr_chat_messages.append({"role": "user", "content": preds})
        
        try:
            chat_completion = openai.ChatCompletion.create(
                model="gpt-4",
                messages=curr_chat_messages,
                timeout=TIMEOUT,
            )
            
            elapsed_time = time.time() - start_time
            if elapsed_time > TIMEOUT:
                print("Timeout exceeded!")
                _dict["response"] = "FAIL"
                save_json_to_file(_dict, responses_savedir / f"{object_id}.json")
                responses.append(json.dumps(_dict))
                unsuccessful_responses += 1
                continue
            
            # Count unsuccessful responses
            if "invalid" in chat_completion["choices"][0]["message"]["content"].strip("\n"):
                unsuccessful_responses += 1
                
            # Print output
            print(f"Object ID: {object_id}")
            print(chat_completion["choices"][0]["message"]["content"])
            print(f"Unsuccessful responses so far: {unsuccessful_responses}")
            
            _dict["response"] = chat_completion["choices"][0]["message"]["content"].strip("\n")
            
            # Save the response
            responses.append(json.dumps(_dict))
            save_json_to_file(_dict, responses_savedir / f"{object_id}.json")
            
        except Exception as e:
            print(f"Error processing object {object_id}: {e}")
            _dict["response"] = f"ERROR: {str(e)}"
            save_json_to_file(_dict, responses_savedir / f"{object_id}.json")
            responses.append(json.dumps(_dict))
            unsuccessful_responses += 1

    # Save all responses to a pickle file
    with open(Path(args.cachedir) / "reshot_gpt-4_responses.pkl", "wb") as f:
        pkl.dump(responses, f)


def visualize_reshot_captions(args):
    """
    Visualize reshot images with their LLaVA captions and GPT-4 refined descriptions.
    Creates a comprehensive visualization showing both the original and refined captions.
    """
    from PIL import Image

    # Load the LLaVA captions
    llava_caption_file = Path(args.cachedir) / "reshot_llava_captions.json"
    if not llava_caption_file.exists():
        raise FileNotFoundError(f"LLaVA captions file not found: {llava_caption_file}")

    with open(llava_caption_file, "r") as f:
        llava_captions = json.load(f)

    # Create a mapping from object ID to captions
    llava_captions_dict = {item["id"]: item["captions"] for item in llava_captions}

    # Check if GPT-4 refined captions exist
    gpt4_responses_dir = Path(args.cachedir) / "reshot_gpt-4_responses"
    has_gpt4_captions = gpt4_responses_dir.exists()

    # Create output directory
    output_dir = Path(args.cachedir) / args.vis_output_dir
    output_dir.mkdir(exist_ok=True, parents=True)

    # Get the reshot directory
    reshot_dir = Path(args.reshot_dir)

    # Get the list of object IDs to process
    object_ids = args.object_ids if args.object_ids is not None else list(llava_captions_dict.keys())

    for object_id in tqdm(object_ids, desc="Creating visualizations"):
        if object_id not in llava_captions_dict:
            print(f"No LLaVA captions found for object {object_id}, skipping...")
            continue

        # Check if reshot images exist for this object
        reshot_images = [
            reshot_dir / f"{object_id}_top{i}.png"
            for i in range(1, 5)  # top1 to top4
        ]

        # Filter out images that don't exist
        reshot_images = [img_path for img_path in reshot_images if img_path.exists()]

        if not reshot_images:
            print(f"No reshot images found for object {object_id}, skipping...")
            continue

        # Get LLaVA captions
        llava_caption_list = llava_captions_dict[object_id]

        # Get GPT-4 refined caption if available
        gpt4_caption = None
        if has_gpt4_captions:
            gpt4_file = gpt4_responses_dir / f"{object_id}.json"
            if gpt4_file.exists():
                try:
                    with open(gpt4_file, "r") as f:
                        gpt4_data = json.load(f)

                    if isinstance(gpt4_data["response"], str):
                        try:
                            gpt4_response = json.loads(gpt4_data["response"])
                            gpt4_caption = {
                                "summary": gpt4_response.get("summary", "No summary available"),
                                "object_tag": gpt4_response.get("object_tag", "No tag available"),
                                "possible_tags": gpt4_response.get("possible_tags", [])
                            }
                        except json.JSONDecodeError:
                            gpt4_caption = {
                                "summary": "Error parsing GPT-4 response",
                                "object_tag": "Error",
                                "possible_tags": []
                            }
                    else:
                        gpt4_caption = gpt4_data["response"]
                except Exception as e:
                    print(f"Error loading GPT-4 caption for object {object_id}: {e}")

        # Create a figure with the images and captions
        n_images = len(reshot_images)

        # Create a larger figure to accommodate images and text
        fig_width = 15
        fig_height = 12 if gpt4_caption else 10

        fig, axs = plt.subplots(2, 2, figsize=(fig_width, fig_height))
        fig.suptitle(f"Object ID: {object_id}", fontsize=16)

        # Flatten axes for easier indexing
        axs = axs.flatten()

        # Plot each image with its LLaVA caption
        for i, img_path in enumerate(reshot_images):
            if i >= 4:  # Only show up to 4 images
                break

            # Load image
            img = Image.open(img_path)

            # Get corresponding caption
            caption = llava_caption_list[i] if i < len(llava_caption_list) else "No caption available"

            # Display image
            axs[i].imshow(np.array(img))
            axs[i].set_title(f"Top {i+1} View", fontsize=14)

            # Add caption as text below the image
            wrapped_caption = '\n'.join(wrap(caption, 40))
            axs[i].text(0.5, -0.1, wrapped_caption,
                      transform=axs[i].transAxes,
                      fontsize=12,
                      ha='center',
                      va='top',
                      wrap=True)

            axs[i].axis('off')

        # If we have fewer than 4 images, hide the unused axes
        for i in range(n_images, 4):
            axs[i].axis('off')

        # Add GPT-4 summary at the bottom if available
        if gpt4_caption:
            summary_text = f"GPT-4 Summary: {gpt4_caption['summary']}\n\n"
            summary_text += f"Object Tag: {gpt4_caption['object_tag']}\n\n"
            summary_text += f"Possible Tags: {', '.join(gpt4_caption['possible_tags'])}"

            # Add a text box at the bottom for the GPT-4 summary
            fig.text(0.5, 0.02, summary_text,
                    ha='center',
                    va='bottom',
                    fontsize=12,
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.3))

        plt.tight_layout()
        if gpt4_caption:
            # Add extra space at the bottom for the GPT-4 summary
            plt.subplots_adjust(bottom=0.25)

        # Save the figure
        output_file = output_dir / f"object_{object_id}_visualization.png"
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()

        print(f"Saved visualization for object {object_id} to {output_file}")

    print(f"Visualizations saved to {output_dir}")


def main():
    # Process command-line args
    args = tyro.cli(ProgramArgs)

    if args.mode == "extract-reshot-captions":
        extract_reshot_captions(args)
    elif args.mode == "refine-reshot-captions":
        refine_reshot_captions(args)
    elif args.mode == "visualize-reshot-captions":
        visualize_reshot_captions(args)
    else:
        raise ValueError(f"Unknown mode: {args.mode}")


if __name__ == "__main__":
    main()
