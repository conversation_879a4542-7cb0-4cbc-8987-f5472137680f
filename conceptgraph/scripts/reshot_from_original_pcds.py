"""
Generate reshot images from original (unprocessed) point clouds.
This script loads the original point clouds saved by the mapping pipeline
and generates high-quality reshot images for LLaVA captioning.
"""

import argparse
import json
import os
from pathlib import Path
import numpy as np
import open3d as o3d
from tqdm import tqdm


def fibonacci_sphere(samples=64):
    """Generate points on a sphere using the Fibonacci spiral method."""
    points = []
    phi = np.pi * (3.0 - np.sqrt(5.0))  # golden angle in radians

    for i in range(samples):
        y = 1 - (i / float(samples - 1)) * 2  # y goes from 1 to -1
        radius = np.sqrt(1 - y * y)  # radius at y

        theta = phi * i  # golden angle increment

        x = np.cos(theta) * radius
        z = np.sin(theta) * radius

        points.append([x, y, z])

    return np.array(points)


def upper_hemisphere_fibonacci(samples=64, radius=1.0):
    """Generate camera positions on the upper hemisphere."""
    sphere_points = fibonacci_sphere(samples * 2)  # Generate more points
    # Filter to keep only upper hemisphere (y >= 0)
    upper_points = sphere_points[sphere_points[:, 1] >= 0]
    # Take the first 'samples' points
    upper_points = upper_points[:samples]
    return upper_points * radius


def visibility_score(pcd, cam_pos):
    """Calculate visibility score for a camera position."""
    points = np.asarray(pcd.points)
    center = pcd.get_center()
    
    # Vector from camera to object center
    view_dir = center - cam_pos
    view_dir = view_dir / np.linalg.norm(view_dir)
    
    # Vectors from camera to each point
    point_dirs = points - cam_pos
    point_distances = np.linalg.norm(point_dirs, axis=1)
    point_dirs = point_dirs / point_distances[:, np.newaxis]
    
    # Dot product gives cosine of angle between view direction and point direction
    dot_products = np.dot(point_dirs, view_dir)
    
    # Points with positive dot product are in front of the camera
    visible_mask = dot_products > 0
    
    # Weight by distance (closer points are more important)
    weights = 1.0 / (1.0 + point_distances)
    
    # Calculate visibility score
    if np.sum(visible_mask) == 0:
        return 0.0
    
    visibility = np.sum(weights[visible_mask] * dot_products[visible_mask]) / np.sum(weights)
    return visibility


def upright_score(view_dir, gravity=np.array([0, 0, 1])):
    """Calculate how upright the view direction is."""
    return 1 - abs(np.dot(view_dir, gravity))


def render_with_visualizer(pcd, cam_pos, output_path, width=512, height=512):
    """Render point cloud from a specific camera position."""
    vis = o3d.visualization.Visualizer()
    vis.create_window(visible=False, width=width, height=height)
    vis.add_geometry(pcd)

    ctr = vis.get_view_control()
    center = pcd.get_center()
    up = np.array([0, 0, 1])
    front = (center - cam_pos)
    front /= np.linalg.norm(front)
    ctr.set_lookat(center)
    ctr.set_front(front)
    ctr.set_up(up)
    ctr.set_zoom(0.7)

    vis.poll_events()
    vis.update_renderer()

    img = vis.capture_screen_float_buffer(False)
    vis.destroy_window()

    img_np = (np.asarray(img) * 255).astype(np.uint8)
    o3d.io.write_image(output_path, o3d.geometry.Image(img_np))
    return img_np


def generate_reshot_images(
    original_pcds_dir: str,
    output_dir: str = "CG/llava_candidate_original",
    top_k: int = 4,
    samples: int = 64,
    up_ratio: float = 0.3
):
    """
    Generate reshot images from original point clouds.
    
    Args:
        original_pcds_dir: Directory containing original point cloud files
        output_dir: Directory to save reshot images
        top_k: Number of top views to generate per object
        samples: Number of candidate camera positions to evaluate
        up_ratio: Weight for upright score vs visibility score
    """
    original_pcds_path = Path(original_pcds_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Find all original point cloud files
    ply_files = list(original_pcds_path.glob("object_*_original.ply"))
    ply_files.sort(key=lambda x: int(x.stem.split('_')[1]))  # Sort by object ID
    
    print(f"Found {len(ply_files)} original point cloud files")
    
    for ply_file in tqdm(ply_files, desc="Generating reshot images"):
        # Extract object ID from filename
        object_id = int(ply_file.stem.split('_')[1])
        
        # Load point cloud
        pcd = o3d.io.read_point_cloud(str(ply_file))
        
        if len(pcd.points) == 0:
            print(f"Warning: Empty point cloud for object {object_id}")
            continue
        
        # Get bounding box and center
        bbox = pcd.get_axis_aligned_bounding_box()
        center = pcd.get_center()
        extent = bbox.get_extent()
        radius = np.linalg.norm(extent) * 1.5
        
        # Generate candidate camera positions
        candidate_dirs = upper_hemisphere_fibonacci(samples=samples, radius=radius)
        candidate_positions = candidate_dirs + center
        
        # Score each candidate
        scores = []
        for cam_pos in candidate_positions:
            view_dir = center - cam_pos
            view_dir /= np.linalg.norm(view_dir)
            vis_score = visibility_score(pcd, cam_pos)
            up_score = upright_score(view_dir)
            final_score = (1 - up_ratio) * vis_score + up_ratio * up_score
            scores.append((final_score, cam_pos))
        
        # Select top-k views
        scores.sort(key=lambda x: x[0], reverse=True)
        top_views = scores[:top_k]
        
        # Render and save top-k views
        for i, (score, cam_pos) in enumerate(top_views):
            output_file = output_path / f"{object_id}_top{i+1}.png"
            render_with_visualizer(pcd, cam_pos, str(output_file))
        
        print(f"Generated {top_k} reshot images for object {object_id}")
    
    print(f"All reshot images saved to {output_path}")


def main():
    parser = argparse.ArgumentParser(description="Generate reshot images from original point clouds")
    parser.add_argument(
        "--original_pcds_dir", 
        type=str, 
        required=True,
        help="Directory containing original point cloud files"
    )
    parser.add_argument(
        "--output_dir", 
        type=str, 
        default="CG/llava_candidate_original",
        help="Directory to save reshot images"
    )
    parser.add_argument(
        "--top_k", 
        type=int, 
        default=4,
        help="Number of top views to generate per object"
    )
    parser.add_argument(
        "--samples", 
        type=int, 
        default=64,
        help="Number of candidate camera positions to evaluate"
    )
    parser.add_argument(
        "--up_ratio", 
        type=float, 
        default=0.3,
        help="Weight for upright score vs visibility score"
    )
    
    args = parser.parse_args()
    
    generate_reshot_images(
        original_pcds_dir=args.original_pcds_dir,
        output_dir=args.output_dir,
        top_k=args.top_k,
        samples=args.samples,
        up_ratio=args.up_ratio
    )


if __name__ == "__main__":
    main()
