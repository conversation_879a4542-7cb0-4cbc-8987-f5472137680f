"""
Test script to verify that the merge_obj2_into_obj1 function works correctly
with the pcd_original field.
"""

import numpy as np
import open3d as o3d
import torch
from types import SimpleNamespace

# Add the parent directory to the path so we can import conceptgraph modules
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from conceptgraph.slam.utils import merge_obj2_into_obj1


def create_test_object(obj_id: int, num_points: int = 100):
    """Create a test object with all necessary fields."""
    # Create random point cloud
    points = np.random.rand(num_points, 3)
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.colors = o3d.utility.Vector3dVector(np.random.rand(num_points, 3))
    
    # Create original point cloud (denser)
    points_orig = np.random.rand(num_points * 2, 3)
    pcd_orig = o3d.geometry.PointCloud()
    pcd_orig.points = o3d.utility.Vector3dVector(points_orig)
    pcd_orig.colors = o3d.utility.Vector3dVector(np.random.rand(num_points * 2, 3))
    
    obj = {
        'pcd': pcd,
        'pcd_original': pcd_orig,
        'bbox': pcd.get_axis_aligned_bounding_box(),
        'clip_ft': torch.randn(512),
        'text_ft': torch.randn(512),
        'num_detections': 5,
        'class_id': [obj_id],
        'inst_color': [1.0, 0.0, 0.0],
        'caption': {0: f"caption for object {obj_id}"}
    }
    
    return obj


def test_merge_function():
    """Test the merge function with pcd_original field."""
    print("Testing merge_obj2_into_obj1 function...")
    
    # Create test configuration
    cfg = SimpleNamespace()
    cfg.downsample_voxel_size = 0.025
    cfg.dbscan_remove_noise = True
    cfg.dbscan_eps = 0.05
    cfg.dbscan_min_points = 10
    cfg.device = 'cpu'
    
    # Create two test objects
    obj1 = create_test_object(1, 50)
    obj2 = create_test_object(2, 30)
    
    print(f"Object 1 - Points: {len(obj1['pcd'].points)}, Original points: {len(obj1['pcd_original'].points)}")
    print(f"Object 2 - Points: {len(obj2['pcd'].points)}, Original points: {len(obj2['pcd_original'].points)}")
    
    # Test the merge function
    try:
        merged_obj = merge_obj2_into_obj1(cfg, obj1, obj2, run_dbscan=False)
        print("✅ Merge function executed successfully!")
        
        print(f"Merged object - Points: {len(merged_obj['pcd'].points)}")
        print(f"Merged object - Original points: {len(merged_obj['pcd_original'].points)}")
        print(f"Merged object - Detections: {merged_obj['num_detections']}")
        
        # Verify that pcd_original was merged correctly
        if 'pcd_original' in merged_obj:
            print("✅ pcd_original field exists in merged object")
        else:
            print("❌ pcd_original field missing in merged object")
            
    except Exception as e:
        print(f"❌ Merge function failed with error: {e}")
        import traceback
        traceback.print_exc()


def test_merge_without_original():
    """Test merge when one object doesn't have pcd_original."""
    print("\nTesting merge when obj1 has no pcd_original...")
    
    cfg = SimpleNamespace()
    cfg.downsample_voxel_size = 0.025
    cfg.dbscan_remove_noise = True
    cfg.dbscan_eps = 0.05
    cfg.dbscan_min_points = 10
    cfg.device = 'cpu'
    
    # Create objects where obj1 doesn't have pcd_original
    obj1 = create_test_object(1, 50)
    obj2 = create_test_object(2, 30)
    del obj1['pcd_original']  # Remove pcd_original from obj1
    
    try:
        merged_obj = merge_obj2_into_obj1(cfg, obj1, obj2, run_dbscan=False)
        print("✅ Merge function executed successfully!")
        
        if 'pcd_original' in merged_obj:
            print("✅ pcd_original field copied from obj2 to merged object")
            print(f"Merged object - Original points: {len(merged_obj['pcd_original'].points)}")
        else:
            print("❌ pcd_original field missing in merged object")
            
    except Exception as e:
        print(f"❌ Merge function failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_merge_function()
    test_merge_without_original()
    print("\nTest completed!")
