name: conceptgraph
channels:
  - https://anaconda.org/pytorch3d/pytorch3d/0.7.4/download
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - _anaconda_depends=2023.09=py310_mkl_1
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - abseil-cpp=20211102.0=hd4dd3e8_0
  - aiobotocore=2.5.0=py310h06a4308_0
  - aiofiles=22.1.0=py310h06a4308_0
  - aiohttp=3.8.5=py310h5eee18b_0
  - aioitertools=0.7.1=pyhd3eb1b0_0
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - aiosqlite=0.18.0=py310h06a4308_0
  - alabaster=0.7.12=pyhd3eb1b0_0
  - anaconda=custom=py310_2
  - aom=3.6.0=h6a678d5_0
  - appdirs=1.4.4=pyhd3eb1b0_0
  - argon2-cffi=21.3.0=pyhd3eb1b0_0
  - argon2-cffi-bindings=21.2.0=py310h7f8727e_0
  - arrow=1.2.3=py310h06a4308_1
  - arrow-cpp=11.0.0=h374c478_2
  - astroid=2.14.2=py310h06a4308_0
  - astropy=5.1=py310ha9d4c09_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - async-timeout=4.0.2=py310h06a4308_0
  - atomicwrites=1.4.0=py_0
  - attrs=22.1.0=py310h06a4308_0
  - automat=20.2.0=py_0
  - autopep8=1.6.0=pyhd3eb1b0_1
  - aws-c-common=0.6.8=h5eee18b_1
  - aws-c-event-stream=0.1.6=h6a678d5_6
  - aws-checksums=0.1.11=h5eee18b_2
  - aws-sdk-cpp=1.8.185=h721c034_1
  - babel=2.11.0=py310h06a4308_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - bcrypt=3.2.0=py310h5eee18b_1
  - beautifulsoup4=4.12.2=py310h06a4308_0
  - binaryornot=0.4.4=pyhd3eb1b0_1
  - black=23.3.0=py310h06a4308_0
  - blas=1.0=mkl
  - bleach=4.1.0=pyhd3eb1b0_0
  - blosc=1.21.3=h6a678d5_0
  - bokeh=3.2.1=py310h2f386ee_0
  - boost-cpp=1.73.0=h7f8727e_12
  - botocore=1.29.76=py310h06a4308_0
  - bottleneck=1.3.5=py310ha9d4c09_0
  - brotli=1.0.9=h5eee18b_7
  - brotli-bin=1.0.9=h5eee18b_7
  - brotlipy=0.7.0=py310h7f8727e_1002
  - brunsli=0.1=h2531618_0
  - bzip2=1.0.8=h7b6447c_0
  - c-ares=1.19.1=h5eee18b_0
  - c-blosc2=2.8.0=h6a678d5_0
  - ca-certificates=2023.7.22=hbcca054_0
  - certifi=2023.7.22=pyhd8ed1ab_0
  - cffi=1.15.1=py310h5eee18b_3
  - cfitsio=3.470=h5893167_7
  - chardet=4.0.0=py310h06a4308_1003
  - charls=2.2.0=h2531618_0
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - click=8.0.4=py310h06a4308_0
  - cloudpickle=2.2.1=py310h06a4308_0
  - colorama=0.4.6=py310h06a4308_0
  - colorcet=3.0.1=py310h06a4308_0
  - comm=0.1.2=py310h06a4308_0
  - constantly=15.1.0=py310h06a4308_0
  - contourpy=1.0.5=py310hdb19cb5_0
  - cookiecutter=1.7.3=pyhd3eb1b0_0
  - cryptography=41.0.3=py310hdda0065_0
  - cssselect=1.1.0=pyhd3eb1b0_0
  - cuda-cudart=11.8.89=0
  - cuda-cupti=11.8.87=0
  - cuda-libraries=11.8.0=0
  - cuda-nvrtc=11.8.89=0
  - cuda-nvtx=11.8.86=0
  - cuda-runtime=11.8.0=0
  - cudatoolkit-dev=11.7.0=h1de0b5d_6
  - curl=8.2.1=hdbd6064_0
  - cycler=0.11.0=pyhd3eb1b0_0
  - cyrus-sasl=2.1.28=h52b45da_1
  - cytoolz=0.12.0=py310h5eee18b_0
  - daal4py=2023.1.1=py310h3c18c91_0
  - dal=2023.1.1=hdb19cb5_48679
  - dask=2023.6.0=py310h06a4308_0
  - dask-core=2023.6.0=py310h06a4308_0
  - dataclasses=0.8=pyhc8e2a94_3
  - datasets=2.12.0=py310h06a4308_0
  - datashader=0.15.2=py310h06a4308_0
  - datashape=0.5.4=py310h06a4308_1
  - dav1d=1.2.1=h5eee18b_0
  - dbus=1.13.18=hb2f20db_0
  - debugpy=1.6.7=py310h6a678d5_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - diff-match-patch=20200713=pyhd3eb1b0_0
  - dill=0.3.6=py310h06a4308_0
  - distributed=2023.6.0=py310h06a4308_0
  - docstring-to-markdown=0.11=py310h06a4308_0
  - docutils=0.18.1=py310h06a4308_3
  - entrypoints=0.4=py310h06a4308_0
  - et_xmlfile=1.1.0=py310h06a4308_0
  - exceptiongroup=1.0.4=py310h06a4308_0
  - executing=0.8.3=pyhd3eb1b0_0
  - expat=2.5.0=h6a678d5_0
  - faiss-cpu=1.7.4=py3.10_h8c27c75_0_cpu
  - ffmpeg=4.3=hf484d3e_0
  - filelock=3.9.0=py310h06a4308_0
  - flake8=6.0.0=py310h06a4308_0
  - flask=2.2.2=py310h06a4308_0
  - font-ttf-dejavu-sans-mono=2.37=hd3eb1b0_0
  - font-ttf-inconsolata=2.001=hcb22688_0
  - font-ttf-source-code-pro=2.030=hd3eb1b0_0
  - font-ttf-ubuntu=0.83=h8b1ccd4_0
  - fontconfig=2.14.1=h4c34cd2_2
  - fonts-anaconda=1=h8fa9717_0
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.12.1=h4a9f257_0
  - frozenlist=1.3.3=py310h5eee18b_0
  - fsspec=2023.4.0=py310h06a4308_0
  - fvcore=0.1.5.post20221221=pyhd8ed1ab_0
  - gensim=4.3.0=py310h1128e8f_0
  - gflags=2.2.2=he6710b0_0
  - giflib=5.2.1=h5eee18b_3
  - glib=2.69.1=he621ea3_2
  - glog=0.5.0=h2531618_0
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py310heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - greenlet=2.0.1=py310h6a678d5_0
  - grpc-cpp=1.48.2=he1ff14a_1
  - gst-plugins-base=1.14.1=h6a678d5_1
  - gstreamer=1.14.1=h5eee18b_1
  - h5py=3.9.0=py310he06866b_0
  - hdf5=1.12.1=h2b7332f_3
  - heapdict=1.0.1=pyhd3eb1b0_0
  - holoviews=1.17.1=py310h06a4308_0
  - huggingface_hub=0.15.1=py310h06a4308_0
  - hvplot=0.8.4=py310h06a4308_0
  - hyperlink=21.0.0=pyhd3eb1b0_0
  - icu=58.2=he6710b0_3
  - idna=3.4=py310h06a4308_0
  - imagecodecs=2023.1.23=py310hc4b7b5f_0
  - imageio=2.31.1=py310h06a4308_0
  - imagesize=1.4.1=py310h06a4308_0
  - imbalanced-learn=0.10.1=py310h06a4308_1
  - importlib-metadata=6.0.0=py310h06a4308_0
  - importlib_metadata=6.0.0=hd3eb1b0_0
  - incremental=21.3.0=pyhd3eb1b0_0
  - inflection=0.5.1=py310h06a4308_0
  - iniconfig=1.1.1=pyhd3eb1b0_0
  - intake=0.6.8=py310h06a4308_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - intervaltree=3.1.0=pyhd3eb1b0_0
  - iopath=0.1.9=pyhd8ed1ab_0
  - ipykernel=6.25.0=py310h2f386ee_0
  - ipython=8.15.0=py310h06a4308_0
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - ipywidgets=8.0.4=py310h06a4308_0
  - isort=5.9.3=pyhd3eb1b0_0
  - itemadapter=0.3.0=pyhd3eb1b0_0
  - itemloaders=1.0.4=pyhd3eb1b0_1
  - itsdangerous=2.0.1=pyhd3eb1b0_0
  - jaraco.classes=3.2.1=pyhd3eb1b0_0
  - jedi=0.18.1=py310h06a4308_1
  - jeepney=0.7.1=pyhd3eb1b0_0
  - jellyfish=1.0.1=py310hb02cf49_0
  - jinja2=3.1.2=py310h06a4308_0
  - jinja2-time=0.2.0=pyhd3eb1b0_3
  - jmespath=0.10.0=pyhd3eb1b0_0
  - joblib=1.2.0=py310h06a4308_0
  - jpeg=9e=h5eee18b_1
  - jq=1.6=h27cfd23_1000
  - json5=0.9.6=pyhd3eb1b0_0
  - jsonschema=4.17.3=py310h06a4308_0
  - jupyter=1.0.0=py310h06a4308_8
  - jupyter_client=7.4.9=py310h06a4308_0
  - jupyter_console=6.6.3=py310h06a4308_0
  - jupyter_core=5.3.0=py310h06a4308_0
  - jupyter_events=0.6.3=py310h06a4308_0
  - jupyter_server=1.23.4=py310h06a4308_0
  - jupyter_server_fileid=0.9.0=py310h06a4308_0
  - jupyter_server_ydoc=0.8.0=py310h06a4308_1
  - jupyter_ydoc=0.2.4=py310h06a4308_0
  - jupyterlab=3.6.3=py310h06a4308_0
  - jupyterlab_pygments=0.1.2=py_0
  - jupyterlab_server=2.22.0=py310h06a4308_0
  - jupyterlab_widgets=3.0.5=py310h06a4308_0
  - jxrlib=1.1=h7b6447c_2
  - kaleido-core=0.2.1=h7c8854e_0
  - keyring=23.13.1=py310h06a4308_0
  - kiwisolver=1.4.4=py310h6a678d5_0
  - krb5=1.20.1=h143b758_1
  - lame=3.100=h7b6447c_0
  - lazy-object-proxy=1.6.0=py310h7f8727e_0
  - lazy_loader=0.2=py310h06a4308_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libaec=1.0.4=he6710b0_1
  - libavif=0.11.1=h5eee18b_0
  - libboost=1.73.0=h28710b8_12
  - libbrotlicommon=1.0.9=h5eee18b_7
  - libbrotlidec=1.0.9=h5eee18b_7
  - libbrotlienc=1.0.9=h5eee18b_7
  - libclang=14.0.6=default_hc6dbbc7_1
  - libclang13=14.0.6=default_he11475f_1
  - libcublas=11.11.3.6=0
  - libcufft=*********=0
  - libcufile=1.7.2.10=0
  - libcups=2.4.2=h2d74bed_1
  - libcurand=10.3.3.141=0
  - libcurl=8.2.1=h251f7ec_0
  - libcusolver=11.4.1.48=0
  - libcusparse=11.7.5.86=0
  - libdeflate=1.17=h5eee18b_0
  - libedit=3.1.20221030=h5eee18b_0
  - libev=4.33=h7f8727e_1
  - libevent=2.1.12=hdbd6064_1
  - libfaiss=1.7.4=h2bc3f7f_0_cpu
  - libffi=3.4.4=h6a678d5_0
  - libgcc-ng=11.2.0=h1234567_1
  - libgfortran-ng=11.2.0=h00389a5_1
  - libgfortran5=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.4=h5eee18b_0
  - libllvm14=14.0.6=hdb19cb5_3
  - libnghttp2=1.52.0=h2d74bed_1
  - libnpp=11.8.0.86=0
  - libnvjpeg=11.9.0.86=0
  - libpng=1.6.39=h5eee18b_0
  - libpq=12.15=hdbd6064_1
  - libprotobuf=3.20.3=he621ea3_0
  - libsodium=1.0.18=h7b6447c_0
  - libspatialindex=1.9.3=h2531618_0
  - libssh2=1.10.0=hdbd6064_2
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libthrift=0.15.0=h1795dd8_2
  - libtiff=4.5.1=h6a678d5_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.41.5=h5eee18b_0
  - libwebp=1.3.2=h11a3e52_0
  - libwebp-base=1.3.2=h5eee18b_0
  - libxcb=1.15=h7f8727e_0
  - libxkbcommon=1.0.1=h5eee18b_1
  - libxml2=2.10.4=hcbfbd50_0
  - libxslt=1.1.37=h2085143_0
  - libzopfli=1.0.3=he6710b0_0
  - linkify-it-py=2.0.0=py310h06a4308_0
  - llvmlite=0.40.0=py310he621ea3_0
  - locket=1.0.0=py310h06a4308_0
  - lxml=4.9.3=py310hdbbb534_0
  - lz4=4.3.2=py310h5eee18b_0
  - lz4-c=1.9.4=h6a678d5_0
  - lzo=2.10=h7b6447c_2
  - markdown=3.4.1=py310h06a4308_0
  - markdown-it-py=2.2.0=py310h06a4308_1
  - markupsafe=2.1.1=py310h7f8727e_0
  - mathjax=2.7.5=h06a4308_0
  - matplotlib=3.7.2=py310h06a4308_0
  - matplotlib-base=3.7.2=py310h1128e8f_0
  - matplotlib-inline=0.1.6=py310h06a4308_0
  - mccabe=0.7.0=pyhd3eb1b0_0
  - mdit-py-plugins=0.3.0=py310h06a4308_0
  - mdurl=0.1.0=py310h06a4308_0
  - mistune=0.8.4=py310h7f8727e_1000
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py310h7f8727e_0
  - mkl_fft=1.3.1=py310hd6ae3a3_0
  - mkl_random=1.2.2=py310h00e6091_0
  - more-itertools=8.12.0=pyhd3eb1b0_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpi=1.0=mpich
  - mpich=4.1.1=hbae89fd_0
  - mpmath=1.3.0=py310h06a4308_0
  - msgpack-python=1.0.3=py310hd09550d_0
  - multidict=6.0.2=py310h5eee18b_0
  - multipledispatch=0.6.0=py310h06a4308_0
  - multiprocess=0.70.14=py310h06a4308_0
  - munkres=1.1.4=py_0
  - mypy_extensions=1.0.0=py310h06a4308_0
  - mysql=5.7.24=h721c034_2
  - nbclassic=0.5.5=py310h06a4308_0
  - nbclient=0.5.13=py310h06a4308_0
  - nbconvert=6.5.4=py310h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.5.6=py310h06a4308_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.1=py310h06a4308_0
  - nltk=3.8.1=py310h06a4308_0
  - notebook=6.5.4=py310h06a4308_1
  - notebook-shim=0.2.2=py310h06a4308_0
  - nspr=4.35=h6a678d5_0
  - nss=3.89.1=h6a678d5_0
  - numba=0.57.1=py310h1128e8f_0
  - numexpr=2.8.4=py310h8879344_0
  - numpy=1.24.3=py310hd5efca6_0
  - numpy-base=1.24.3=py310h8e6c178_0
  - numpydoc=1.5.0=py310h06a4308_0
  - oniguruma=*******=h27cfd23_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.4.0=h3ad879b_0
  - openpyxl=3.0.10=py310h5eee18b_0
  - openssl=3.0.11=h7f8727e_2
  - orc=1.7.4=hb3bc3d3_1
  - packaging=23.1=py310h06a4308_0
  - pandas=2.0.3=py310h1128e8f_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - panel=1.2.3=py310h06a4308_0
  - param=1.13.0=py310h06a4308_0
  - parsel=1.6.0=py310h06a4308_0
  - parso=0.8.3=pyhd3eb1b0_0
  - partd=1.4.0=py310h06a4308_0
  - pathspec=0.10.3=py310h06a4308_0
  - patsy=0.5.3=py310h06a4308_0
  - pcre=8.45=h295c915_0
  - pep8=1.7.1=py310h06a4308_1
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.4.0=py310h6a678d5_1
  - pip=23.2.1=py310h06a4308_0
  - platformdirs=3.10.0=py310h06a4308_0
  - plotly=5.9.0=py310h06a4308_0
  - pluggy=1.0.0=py310h06a4308_1
  - ply=3.11=py310h06a4308_0
  - pooch=1.4.0=pyhd3eb1b0_0
  - portalocker=2.8.2=py310hff52083_1
  - poyo=0.5.0=pyhd3eb1b0_0
  - prometheus_client=0.14.1=py310h06a4308_0
  - prompt-toolkit=3.0.36=py310h06a4308_0
  - prompt_toolkit=3.0.36=hd3eb1b0_0
  - protego=0.1.16=py_0
  - psutil=5.9.0=py310h5eee18b_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - py-cpuinfo=8.0.0=pyhd3eb1b0_1
  - pyarrow=11.0.0=py310h468efa6_1
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycodestyle=2.10.0=py310h06a4308_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyct=0.5.0=py310h06a4308_0
  - pycurl=7.45.2=py310hdbd6064_1
  - pydispatcher=2.0.5=py310h06a4308_2
  - pydocstyle=6.3.0=py310h06a4308_0
  - pyerfa=2.0.0=py310h7f8727e_0
  - pyflakes=3.0.1=py310h06a4308_0
  - pygments=2.15.1=py310h06a4308_1
  - pylint=2.16.2=py310h06a4308_0
  - pylint-venv=2.3.0=py310h06a4308_0
  - pyls-spyder=0.4.0=pyhd3eb1b0_0
  - pyodbc=4.0.34=py310h6a678d5_0
  - pyopenssl=23.2.0=py310h06a4308_0
  - pyparsing=3.0.9=py310h06a4308_0
  - pyqt=5.15.7=py310h6a678d5_1
  - pyqtwebengine=5.15.7=py310h6a678d5_1
  - pyrsistent=0.18.0=py310h7f8727e_0
  - pysocks=1.7.1=py310h06a4308_0
  - pytables=3.8.0=py310hb8ae3fc_3
  - pytest=7.4.0=py310h06a4308_0
  - python=3.10.13=h955ad1f_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py310h06a4308_0
  - python-json-logger=2.0.7=py310h06a4308_0
  - python-kaleido=0.2.1=py310h06a4308_0
  - python-lmdb=1.4.1=py310h6a678d5_0
  - python-lsp-black=1.2.1=py310h06a4308_0
  - python-lsp-jsonrpc=1.0.0=pyhd3eb1b0_0
  - python-lsp-server=1.7.2=py310h06a4308_0
  - python-slugify=5.0.2=pyhd3eb1b0_0
  - python-snappy=0.6.1=py310h6a678d5_0
  - python-tzdata=2023.3=pyhd3eb1b0_0
  - python-xxhash=2.0.2=py310h5eee18b_1
  - python_abi=3.10=2_cp310
  - pytoolconfig=1.2.5=py310h06a4308_1
  - pytorch=2.0.1=py3.10_cuda11.8_cudnn8.7.0_0
  - pytorch-cuda=11.8=h7e8668a_5
  - pytorch-mutex=1.0=cuda
  - pytorch3d=0.7.4=py310_cu118_pyt201
  - pytz=2023.3.post1=py310h06a4308_0
  - pyviz_comms=2.3.0=py310h06a4308_0
  - pywavelets=1.4.1=py310h5eee18b_0
  - pyxdg=0.27=pyhd3eb1b0_0
  - pyyaml=6.0=py310h5eee18b_1
  - pyzmq=23.2.0=py310h6a678d5_0
  - qdarkstyle=3.0.2=pyhd3eb1b0_0
  - qstylizer=0.2.2=py310h06a4308_0
  - qt-main=5.15.2=h7358343_9
  - qt-webengine=5.15.9=h9ab4d14_7
  - qtawesome=1.2.2=py310h06a4308_0
  - qtconsole=5.4.2=py310h06a4308_0
  - qtpy=2.2.0=py310h06a4308_0
  - qtwebkit=5.212=h3fafdc1_5
  - queuelib=1.5.0=py310h06a4308_0
  - re2=2022.04.01=h295c915_0
  - readline=8.2=h5eee18b_0
  - regex=2022.7.9=py310h5eee18b_0
  - requests=2.31.0=py310h06a4308_0
  - requests-file=1.5.1=pyhd3eb1b0_0
  - responses=0.13.3=pyhd3eb1b0_0
  - rfc3339-validator=0.1.4=py310h06a4308_0
  - rfc3986-validator=0.1.1=py310h06a4308_0
  - rope=1.7.0=py310h06a4308_0
  - rtree=1.0.1=py310h06a4308_0
  - s3fs=2023.4.0=py310h06a4308_0
  - safetensors=0.3.2=py310hb02cf49_0
  - scikit-image=0.20.0=py310h6a678d5_0
  - scikit-learn-intelex=2023.1.1=py310h06a4308_0
  - scipy=1.10.1=py310hd5efca6_0
  - scrapy=2.8.0=py310h06a4308_0
  - seaborn=0.12.2=py310h06a4308_0
  - secretstorage=3.3.1=py310h06a4308_1
  - send2trash=1.8.0=pyhd3eb1b0_1
  - service_identity=18.1.0=pyhd3eb1b0_1
  - setuptools=68.0.0=py310h06a4308_0
  - sip=6.6.2=py310h6a678d5_0
  - six=1.16.0=pyhd3eb1b0_1
  - smart_open=5.2.1=py310h06a4308_0
  - snappy=1.1.9=h295c915_0
  - sniffio=1.2.0=py310h06a4308_1
  - snowballstemmer=2.2.0=pyhd3eb1b0_0
  - sortedcontainers=2.4.0=pyhd3eb1b0_0
  - soupsieve=2.4=py310h06a4308_0
  - sphinx=5.0.2=py310h06a4308_0
  - sphinxcontrib-applehelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-devhelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-htmlhelp=2.0.0=pyhd3eb1b0_0
  - sphinxcontrib-jsmath=1.0.1=pyhd3eb1b0_0
  - sphinxcontrib-qthelp=1.0.3=pyhd3eb1b0_0
  - sphinxcontrib-serializinghtml=1.1.5=pyhd3eb1b0_0
  - spyder=5.4.3=py310h06a4308_1
  - spyder-kernels=2.4.4=py310h06a4308_0
  - sqlalchemy=1.4.39=py310h5eee18b_0
  - sqlite=3.41.2=h5eee18b_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - statsmodels=0.14.0=py310ha9d4c09_0
  - sympy=1.11.1=py310h06a4308_0
  - tabulate=0.8.10=py310h06a4308_0
  - tbb=2021.8.0=hdb19cb5_0
  - tbb4py=2021.8.0=py310hdb19cb5_0
  - tblib=1.7.0=pyhd3eb1b0_0
  - tenacity=8.2.2=py310h06a4308_0
  - termcolor=2.3.0=pyhd8ed1ab_0
  - terminado=0.17.1=py310h06a4308_0
  - text-unidecode=1.3=pyhd3eb1b0_0
  - textdistance=4.2.1=pyhd3eb1b0_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - three-merge=0.1.1=pyhd3eb1b0_0
  - tifffile=2023.4.12=py310h06a4308_0
  - tinycss2=1.2.1=py310h06a4308_0
  - tk=8.6.12=h1ccaba5_0
  - tldextract=3.2.0=pyhd3eb1b0_0
  - toml=0.10.2=pyhd3eb1b0_0
  - tomli=2.0.1=py310h06a4308_0
  - tomlkit=0.11.1=py310h06a4308_0
  - toolz=0.12.0=py310h06a4308_0
  - torchaudio=2.0.2=py310_cu118
  - torchtriton=2.0.0=py310
  - tornado=6.3.2=py310h5eee18b_0
  - tqdm=4.65.0=py310h2f386ee_0
  - traitlets=5.7.1=py310h06a4308_0
  - twisted=22.10.0=py310h5eee18b_0
  - typing-extensions=4.7.1=py310h06a4308_0
  - typing_extensions=4.7.1=py310h06a4308_0
  - tzdata=2023c=h04d1e81_0
  - uc-micro-py=1.0.1=py310h06a4308_0
  - ujson=5.4.0=py310h6a678d5_0
  - unidecode=1.2.0=pyhd3eb1b0_0
  - unixodbc=2.3.11=h5eee18b_0
  - urllib3=1.26.16=py310h06a4308_0
  - utf8proc=2.6.1=h27cfd23_0
  - w3lib=1.21.0=pyhd3eb1b0_0
  - watchdog=2.1.6=py310h06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py310h06a4308_1
  - websocket-client=0.58.0=py310h06a4308_4
  - werkzeug=2.2.3=py310h06a4308_0
  - whatthepatch=1.0.2=py310h06a4308_0
  - wheel=0.38.4=py310h06a4308_0
  - widgetsnbextension=4.0.5=py310h06a4308_0
  - wrapt=1.14.1=py310h5eee18b_0
  - wurlitzer=3.0.2=py310h06a4308_0
  - xarray=2023.6.0=py310h06a4308_0
  - xxhash=0.8.0=h7f8727e_3
  - xyzservices=2022.9.0=py310h06a4308_1
  - xz=5.4.2=h5eee18b_0
  - y-py=0.5.9=py310h52d8a92_0
  - yacs=0.1.8=pyhd8ed1ab_0
  - yaml=0.2.5=h7b6447c_0
  - yapf=0.31.0=pyhd3eb1b0_0
  - yarl=1.8.1=py310h5eee18b_0
  - ypy-websocket=0.8.2=py310h06a4308_0
  - zeromq=4.3.4=h2531618_0
  - zfp=1.0.0=h6a678d5_0
  - zict=2.2.0=py310h06a4308_0
  - zipp=3.11.0=py310h06a4308_0
  - zlib=1.2.13=h5eee18b_0
  - zlib-ng=2.0.7=h5eee18b_0
  - zope=1.0=py310h06a4308_1
  - zope.interface=5.4.0=py310h7f8727e_0
  - zstd=1.5.5=hc292b87_0
  - pip:
      - accelerate==0.21.0
      - addict==2.4.0
      - ai2thor-colab==0.1.2
      - altair==5.1.1
      - ansi2html==1.8.0
      - antlr4-python3-runtime==4.9.3
      - anyio==3.7.1
      - aws-requests-auth==0.4.3
      - bitsandbytes==0.41.0
      - chamferdist==1.0.0
      - cmake==3.27.5
      - configargparse==1.7
      - dash==2.13.0
      - dash-core-components==2.0.0
      - dash-html-components==2.0.0
      - dash-table==5.0.0
      - decorator==4.4.2
      - deepspeed==0.9.5
      - deprecated==1.2.14
      - diffusers==0.21.2
      - distinctipy==1.2.3
      - docker-pycreds==0.4.0
      - docstring-parser==0.15
      - einops==0.6.1
      - einops-exts==0.0.4
      - fairscale==0.4.4
      - fastapi==0.103.1
      - ffmpy==0.3.1
      - ftfy==6.1.1
      - gitdb==4.0.10
      - gitpython==3.1.37
      - gradio==3.35.2
      - gradio-client==0.2.9
      - gradslam==1.0.0
      - h11==0.14.0
      - hjson==3.1.0
      - httpcore==0.17.3
      - httpx==0.24.0
      - hydra-core==1.3.2
      - imageio-ffmpeg==0.4.9
      - kornia==0.7.0
      - lit==17.0.1
      - llava==1.0.2
      - markdown2==2.4.10
      - moviepy==1.0.3
      - natsort==8.4.0
      - nbformat==5.7.0
      - ninja==1.11.1
      - nvidia-cublas-cu11==**********
      - nvidia-cuda-cupti-cu11==11.7.101
      - nvidia-cuda-nvrtc-cu11==11.7.99
      - nvidia-cuda-runtime-cu11==11.7.99
      - nvidia-cudnn-cu11==********
      - nvidia-cufft-cu11==*********
      - nvidia-curand-cu11==**********
      - nvidia-cusolver-cu11==********
      - nvidia-cusparse-cu11==*********
      - nvidia-nccl-cu11==2.14.3
      - nvidia-nvtx-cu11==11.7.91
      - omegaconf==2.3.0
      - open-clip-torch==2.20.0
      - open3d==0.17.0
      - openai==0.28.1
      - opencv-python==********
      - opencv-python-headless==********
      - orjson==3.9.7
      - pathtools==0.1.2
      - peft==0.4.0
      - prior==1.0.3
      - proglog==0.1.10
      - progressbar2==4.2.0
      - protobuf==3.20.3
      - pycocoevalcap==1.2
      - pycocotools==2.0.7
      - pydantic==1.10.12
      - pydub==0.25.1
      - pygithub==2.1.1
      - pyjwt==2.8.0
      - pynacl==1.5.0
      - pyqt5-sip==12.11.0
      - pyquaternion==0.9.9
      - python-multipart==0.0.6
      - python-utils==3.8.1
      - python-xlib==0.33
      - retrying==1.3.4
      - rich==13.5.3
      - sacremoses==0.0.53
      - scikit-learn==1.2.2
      - semantic-version==2.10.0
      - sentencepiece==0.1.99
      - sentry-sdk==1.31.0
      - setproctitle==1.3.2
      - shortuuid==1.0.11
      - shtab==1.6.4
      - smmap==5.0.1
      - starlette==0.27.0
      - supervision==0.14.0
      - svgwrite==1.4.3
      - timm==0.6.13
      - tokenizers==0.13.3
      - torch==2.0.1
      - torchvision==0.15.2
      - transformers==4.31.0
      - triton==2.0.0
      - tyro==0.5.9
      - uvicorn==0.23.2
      - wandb==0.15.11
      - wavedrom==2.0.3.post3
      - websockets==11.0.3
