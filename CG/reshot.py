import open3d as o3d
import numpy as np
import matplotlib.pyplot as plt
import os

base_dir = "cfslam_captions_llava_debug"
max_index = 60
up_ratio = 0.2


def upper_hemisphere_fibon<PERSON>ci(samples=64, radius=1.5):
    points = []
    offset = 2. / samples
    increment = np.pi * (3. - np.sqrt(5))
    i = 0
    attempts = 0
    max_attempts = samples * 10
    while len(points) < samples and attempts < max_attempts:
        y = i * offset - 1 + offset / 2
        r = np.sqrt(np.clip(1 - y * y, 0, 1))
        phi = i * increment
        x = np.cos(phi) * r
        z = np.sin(phi) * r
        if y > 0:
            i += 1
            attempts += 1
            continue
        # Map to Z-up coordinates: (x, y, z) → (x, z, y)
        points.append([x * radius, z * radius, y * radius])
        i += 1
        attempts += 1
    return np.array(points)


def visibility_score(pcd, cam_location, radius=100.0):
    try:
        _, pt_map = pcd.hidden_point_removal(cam_location, radius)
        return len(pt_map) / len(pcd.points)
    except Exception as e:
        print(f"[warn] visibility_score failed at {cam_location}: {e}")
        return 0.0


def upright_score(view_dir, gravity=np.array([0, 0, 1])):
    return 1 - abs(np.dot(view_dir, gravity))  # closer to horizontal = higher score


def render_offscreen(pcd, cam_pos, output_path, width=512, height=512):
    center = pcd.get_center()
    vis = o3d.visualization.rendering.OffscreenRenderer(width, height)
    scene = vis.scene
    mat = o3d.visualization.rendering.MaterialRecord()
    mat.shader = "defaultUnlit"
    scene.set_background([1.0, 1.0, 1.0, 1.0])
    scene.add_geometry("object", pcd, mat)

    view_dir = center - cam_pos
    view_dir /= np.linalg.norm(view_dir)
    up = np.array([0, 0, 1])  # Z-axis is "up"
    vis.setup_camera(60.0, center, cam_pos, up)

    img = vis.render_to_image()
    o3d.io.write_image(output_path, img)
    return np.asarray(img)

def render_with_visualizer(pcd, cam_pos, output_path, width=512, height=512):
    vis = o3d.visualization.Visualizer()
    vis.create_window(visible=False, width=width, height=height)
    vis.add_geometry(pcd)

    ctr = vis.get_view_control()
    center = pcd.get_center()
    up = np.array([0, 0, 1])
    front = (center - cam_pos)
    front /= np.linalg.norm(front)
    ctr.set_lookat(center)
    ctr.set_front(front)
    ctr.set_up(up)
    ctr.set_zoom(0.7)

    vis.poll_events()
    vis.update_renderer()

    img = vis.capture_screen_float_buffer(False)
    vis.destroy_window()

    img_np = (np.asarray(img) * 255).astype(np.uint8)
    o3d.io.write_image(output_path, o3d.geometry.Image(img_np))
    return img_np

def load_ply(index):
    ply_path = os.path.join(base_dir, f"{index}.ply")
    if os.path.exists(ply_path):
        pcd = o3d.io.read_point_cloud(ply_path)
        print(f"!!! Loaded {ply_path} with {len(pcd.points)} points")
        return pcd
    else:
        print(f"Point cloud not found: {ply_path}")
        return None


def main():
      
    top_k = 4
    samples = 64
    while True:
        cmd = input(f"\n### Enter index (0 to {max_index}) or q (quit): ").strip()
        if cmd == 'q':
            break
        if not cmd.isdigit():
            print("Invalid input, please enter a number or 'q'.")
            continue
        index = int(cmd)
        if index < 0 or index > max_index:
            print(f"Index out of range, please enter between 0 and {max_index}.")
            continue
        pcd = load_ply(index)
        bbox = pcd.get_axis_aligned_bounding_box()
        radius = np.linalg.norm(bbox.get_extent()) * 1.5
        center = pcd.get_center()

        # Generate camera positions
        candidate_dirs = upper_hemisphere_fibonacci(samples=samples, radius=radius)
        candidate_positions = candidate_dirs + center

        # Score each candidate
        scores = []
        for cam_pos in candidate_positions:
            view_dir = center - cam_pos
            view_dir /= np.linalg.norm(view_dir)
            vis_score = visibility_score(pcd, cam_pos)
            up_score = upright_score(view_dir)
            final_score = (1 - up_ratio) * vis_score + up_ratio * up_score
            scores.append((final_score, cam_pos))

        # Select top-k views
        scores.sort(key=lambda x: x[0], reverse=True)
        top_views = scores[:top_k]

        # Render and save top-k views
        os.makedirs("top_views", exist_ok=True)
        images = []
        for i, (score, cam_pos) in enumerate(top_views):
            out_path = f"top_views/object{index}_view_{i+1}_score_{score:.2f}.png"
            img = render_with_visualizer(pcd, cam_pos, out_path)
            images.append((img, score))

        # Visualize
        fig, axes = plt.subplots(1, top_k, figsize=(4 * top_k, 4))
        for i, (img, score) in enumerate(images):
            axes[i].imshow(img)
            axes[i].axis("off")
            axes[i].set_title(f"object{index}\nTop-{i+1}\nScore: {score:.2f}")
        plt.tight_layout()
        plt.show()
        
    plt.close('all')

if __name__ == "__main__":
    main()
