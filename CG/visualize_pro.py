import open3d as o3d
import os
import re
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

base_dir = "cfslam_captions_llava_debug"

def get_max_index():
    ply_files = [f for f in os.listdir(base_dir) if f.endswith(".ply")]
    indices = []
    for name in ply_files:
        match = re.match(r"(\d+)\.ply", name)
        if match:
            indices.append(int(match.group(1)))
    if not indices:
        raise ValueError("No .ply files found.")
    return max(indices)

def load_ply(index):
    ply_path = os.path.join(base_dir, f"{index}.ply")
    if os.path.exists(ply_path):
        pcd = o3d.io.read_point_cloud(ply_path)
        print(f"!!! Loaded {ply_path} with {len(pcd.points)} points")
        return pcd
    else:
        print(f"Point cloud not found: {ply_path}")
        return None

def load_image(index):
    img_path = os.path.join(base_dir, f"{index}.png")
    if os.path.exists(img_path):
        return mpimg.imread(img_path)
    else:
        print(f"Image not found: {img_path}")
        return None

def show_image(img, index):
    plt.figure(f"Image {index}.png")
    plt.imshow(img)
    plt.axis('off')
    plt.show(block=False)  # Non-blocking show

def main():
    max_index = get_max_index()
    print(f"... Found max index: {max_index}")

    while True:
        cmd = input(f"\n### Enter index (0 to {max_index}) or q (quit): ").strip()
        if cmd == 'q':
            break
        if not cmd.isdigit():
            print("Invalid input, please enter a number or 'q'.")
            continue

        index = int(cmd)
        if index < 0 or index > max_index:
            print(f"Index out of range, please enter between 0 and {max_index}.")
            continue

        img = load_image(index)
        if img is not None:
            show_image(img, index)
        else:
            plt.close('all') 

        pcd = load_ply(index)
        if pcd is not None and len(pcd.points) > 0:
            axis = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.2, origin=[0, 0, 0])
            
            o3d.visualization.draw_geometries([pcd, axis], window_name=f"{index}.ply")
        else:
            print("No valid point cloud to show.")

    plt.close('all')

if __name__ == "__main__":
    main()
