import open3d as o3d
import numpy as np
import matplotlib.pyplot as plt
import os

ply_index = 14
up_ratio = 0.2

def fibonacci_sphere(samples=64, radius=1.5):
    points = []
    offset = 2. / samples
    increment = np.pi * (3. - np.sqrt(5))
    for i in range(samples):
        y = ((i * offset) - 1) + (offset / 2)
        r = np.sqrt(1 - y * y)
        phi = i * increment
        x = np.cos(phi) * r
        z = np.sin(phi) * r
        points.append([x * radius, y * radius, z * radius])
    return np.array(points)

def upper_hemisphere_fibonacci(samples=64, radius=1.5):
    points = []
    offset = 2. / samples
    increment = np.pi * (3. - np.sqrt(5))
    i = 0
    attempts = 0
    max_attempts = samples * 10  # 防止死循环
    while len(points) < samples and attempts < max_attempts:
        y = i * offset - 1 + offset / 2
        i += 1
        if y < 0:
            attempts += 1
            continue
        r = np.sqrt(np.clip(1 - y * y, 0, 1))
        phi = i * increment
        x = np.cos(phi) * r
        z = np.sin(phi) * r
        points.append([x * radius, y * radius, z * radius])
        attempts += 1
    return np.array(points)

def new_upper_hemisphere_fibonacci(samples=64, radius=1.5):
    points = []
    offset = 2. / samples
    increment = np.pi * (3. - np.sqrt(5))
    for i in range(samples * 2):  # 多采一些点
        y = i * offset - 1 + offset / 2
        r = np.sqrt(np.clip(1 - y * y, 0, 1))
        phi = i * increment
        x = np.cos(phi) * r
        z = np.sin(phi) * r
        if y < 0: continue  # 原始 y > 0
        # 原始坐标 (x, y, z)，我们将它映射到 (x, z, -y) 使 z 成为“朝上”
        points.append([x * radius, z * radius, y * radius])
        if len(points) >= samples:
            break
    return np.array(points)


def upright_score(view_dir, gravity=np.array([0, 1, 0])):
    # view_dir: normalized vector from camera to object
    dot = np.dot(view_dir, gravity)
    return (1 - abs(dot))  # 越靠近水平，得分越高


def visibility_score(pcd, cam_location, radius=100.0):
    _, pt_map = pcd.hidden_point_removal(cam_location, radius)
    return len(pt_map) / len(pcd.points)

def render_views(pcd, views, ply_index, save_dir="top_views", zoom=0.7):
    os.makedirs(save_dir, exist_ok=True)
    vis = o3d.visualization.Visualizer()
    vis.create_window(visible=False)
    vis.add_geometry(pcd)

    images = []
    for idx, (score, cam_pos) in enumerate(views):
        ctr = vis.get_view_control()
        front = (pcd.get_center() - cam_pos)
        front /= np.linalg.norm(front)
        up = np.array([0.0, 1.0, 0.0])
        ctr.set_lookat(pcd.get_center())
        ctr.set_front(front)
        ctr.set_up(up)
        ctr.set_zoom(zoom)

        vis.poll_events()
        vis.update_renderer()
        img = vis.capture_screen_float_buffer(False)
        img = (np.asarray(img) * 255).astype(np.uint8)

        filename = os.path.join(save_dir, f"object{ply_index}_view_{idx+1}_score_{score:.2f}.png")
        plt.imsave(filename, img)
        images.append((img, score))

    vis.destroy_window()
    return images

def plot_top_views(images):
    k = len(images)
    fig, axes = plt.subplots(1, k, figsize=(4 * k, 4))
    if k == 1:
        axes = [axes]
    for i, (img, score) in enumerate(images):
        axes[i].imshow(img)
        axes[i].axis("off")
        axes[i].set_title(f"Top-{i+1}\nScore: {score:.2f}")
    plt.tight_layout()
    plt.show()

def main():
    # === Replace with your actual .ply path ===
    base_dir = "cfslam_captions_llava_debug"  
    ply_path = base_dir + f"/{ply_index}.ply"
    top_k = 4

    # Load and normalize
    pcd = o3d.io.read_point_cloud(ply_path)
    bbox = pcd.get_axis_aligned_bounding_box()
    radius = np.linalg.norm(bbox.get_extent()) * 1.5
    centroid = pcd.get_center()

    # Generate candidate views
    candidate_dirs = new_upper_hemisphere_fibonacci(samples=64, radius=radius)
    candidate_positions = candidate_dirs + centroid

    # Score each candidate
    # scores = []
    # for cam_pos in candidate_positions:
    #     score = visibility_score(pcd, cam_pos)
    #     scores.append((score, cam_pos))
    
    scores = []
    for cam_pos in candidate_positions:
        view_dir = (pcd.get_center() - cam_pos)
        view_dir /= np.linalg.norm(view_dir)
        vis_score = visibility_score(pcd, cam_pos)
        up_score = upright_score(view_dir)
        final_score = (1 - up_ratio) * vis_score + up_ratio * up_score
        scores.append((final_score, cam_pos))


    scores.sort(key=lambda x: x[0], reverse=True)
    top_views = scores[:top_k]

    # Render and visualize top-k
    rendered = render_views(pcd, top_views, ply_index, save_dir="top_views")
    plot_top_views(rendered)

if __name__ == "__main__":
    main()
