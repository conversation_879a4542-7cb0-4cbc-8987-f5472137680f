import open3d as o3d
import numpy as np
import matplotlib.pyplot as plt
import os
import re

# base_dir = "cfslam_captions_llava_debug"
# output_dir = "llava_candidate"
base_dir = "/home/<USER>/phd_ws/dataset/Replica/room0/original_pcds"
output_dir = "llava_candidate_original"
up_ratio = 0.2
top_k = 4
samples = 64

def get_all_ply_indices():
    """获取所有ply文件的索引"""
    ply_files = [f for f in os.listdir(base_dir) if f.endswith(".ply")]
    indices = []
    for name in ply_files:
        match = re.match(r"(\d+)\.ply", name)
        if match:
            indices.append(int(match.group(1)))
    return sorted(indices)

def upper_hemisphere_fibonacci(samples=64, radius=1.5):
    points = []
    offset = 2. / samples
    increment = np.pi * (3. - np.sqrt(5))
    i = 0
    attempts = 0
    max_attempts = samples * 10
    while len(points) < samples and attempts < max_attempts:
        y = i * offset - 1 + offset / 2
        r = np.sqrt(np.clip(1 - y * y, 0, 1))
        phi = i * increment
        x = np.cos(phi) * r
        z = np.sin(phi) * r
        if y > 0:
            i += 1
            attempts += 1
            continue
        points.append([x * radius, z * radius, y * radius])
        i += 1
        attempts += 1
    return np.array(points)

def visibility_score(pcd, cam_location, radius=100.0):
    try:
        _, pt_map = pcd.hidden_point_removal(cam_location, radius)
        return len(pt_map) / len(pcd.points)
    except Exception as e:
        print(f"[warn] visibility_score failed at {cam_location}: {e}")
        return 0.0

def upright_score(view_dir, gravity=np.array([0, 0, 1])):
    return 1 - abs(np.dot(view_dir, gravity))

def render_with_visualizer(pcd, cam_pos, output_path, width=512, height=512):
    vis = o3d.visualization.Visualizer()
    vis.create_window(visible=False, width=width, height=height)
    vis.add_geometry(pcd)

    ctr = vis.get_view_control()
    center = pcd.get_center()
    up = np.array([0, 0, 1])
    front = (center - cam_pos)
    front /= np.linalg.norm(front)
    ctr.set_lookat(center)
    ctr.set_front(front)
    ctr.set_up(up)
    ctr.set_zoom(0.7)

    vis.poll_events()
    vis.update_renderer()

    img = vis.capture_screen_float_buffer(False)
    vis.destroy_window()

    img_np = (np.asarray(img) * 255).astype(np.uint8)
    o3d.io.write_image(output_path, o3d.geometry.Image(img_np))
    return img_np

def process_single_ply(index):
    """处理单个ply文件"""
    ply_path = os.path.join(base_dir, f"{index}.ply")
    if not os.path.exists(ply_path):
        print(f"❌ {ply_path} not found, skipping...")
        return
    
    pcd = o3d.io.read_point_cloud(ply_path)
    if len(pcd.points) == 0:
        print(f"❌ {ply_path} is empty, skipping...")
        return
    
    print(f"🔄 Processing {index}.ply with {len(pcd.points)} points...")
    
    bbox = pcd.get_axis_aligned_bounding_box()
    radius = np.linalg.norm(bbox.get_extent()) * 1.5
    center = pcd.get_center()

    candidate_dirs = upper_hemisphere_fibonacci(samples=samples, radius=radius)
    candidate_positions = candidate_dirs + center

    scores = []
    for cam_pos in candidate_positions:
        view_dir = center - cam_pos
        view_dir /= np.linalg.norm(view_dir)
        vis_score = visibility_score(pcd, cam_pos)
        up_score = upright_score(view_dir)
        final_score = (1 - up_ratio) * vis_score + up_ratio * up_score
        scores.append((final_score, cam_pos))

    scores.sort(key=lambda x: x[0], reverse=True)
    top_views = scores[:top_k]

    for i, (score, cam_pos) in enumerate(top_views):
        out_path = os.path.join(output_dir, f"{index}_top{i+1}.png")
        render_with_visualizer(pcd, cam_pos, out_path)
    
    print(f"✅ Saved {top_k} views for object {index}")

def main():
    os.makedirs(output_dir, exist_ok=True)
    
    indices = get_all_ply_indices()
    if not indices:
        print("❌ No ply files found!")
        return
    
    print(f"📦 Found {len(indices)} ply files: {indices}")
    
    for i, index in enumerate(indices):
        print(f"\n[{i+1}/{len(indices)}] Processing object {index}...")
        process_single_ply(index)
    
    print(f"\n🎉 All done! Results saved in '{output_dir}/' folder")

if __name__ == "__main__":
    main()