import open3d as o3d
import os
import re
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

base_dir = "cfslam_captions_llava_debug"  # <-- set your directory here

def get_max_index():
    ply_files = [f for f in os.listdir(base_dir) if f.endswith(".ply")]
    indices = []
    for name in ply_files:
        match = re.match(r"(\d+)\.ply", name)
        if match:
            indices.append(int(match.group(1)))
    if not indices:
        raise ValueError("❌ No .ply files found.")
    return max(indices)

def load_ply(index):
    ply_path = os.path.join(base_dir, f"{index}.ply")
    if os.path.exists(ply_path):
        pcd = o3d.io.read_point_cloud(ply_path)
        print(f"✔️ Loaded {ply_path} with {len(pcd.points)} points")
        return pcd
    else:
        print(f"❌ Point cloud not found: {ply_path}")
        return None

def load_image(index):
    img_path = os.path.join(base_dir, f"{index}.png")
    if os.path.exists(img_path):
        return mpimg.imread(img_path)
    else:
        print(f"❌ Image not found: {img_path}")
        return None

class Viewer:
    def __init__(self, max_index):
        self.index = 0
        self.max_index = max_index

        # Open3D Visualizer
        self.vis = o3d.visualization.Visualizer()
        self.vis.create_window(window_name="PointCloud", width=960, height=720)

        # Initial geometry load
        init_pcd = load_ply(self.index)
        if init_pcd is None or len(init_pcd.points) == 0:
            raise RuntimeError("❌ Initial point cloud is empty.")
        if not init_pcd.has_colors():
            init_pcd.colors = o3d.utility.Vector3dVector(np.ones((len(init_pcd.points), 3)) * 0.5)

        self.pcd = init_pcd
        self.vis.add_geometry(self.pcd)

        # Matplotlib image window (non-blocking)
        plt.ion()
        self.fig, self.ax = plt.subplots()
        img = load_image(self.index)
        if img is None:
            img = np.zeros((10, 10, 3))
        self.im = self.ax.imshow(img)
        self.ax.axis('off')
        self.fig.canvas.manager.set_window_title("Image")

    def update(self):
        print(f"\n📍 Showing {self.index}.ply and {self.index}.png")
        pcd_new = load_ply(self.index)
        img_new = load_image(self.index)

        if pcd_new is not None and len(pcd_new.points) > 0:
            self.pcd.points = pcd_new.points
            self.pcd.colors = (
                pcd_new.colors if pcd_new.has_colors()
                else o3d.utility.Vector3dVector(np.ones((len(pcd_new.points), 3)) * 0.5)
            )

            self.vis.update_geometry(self.pcd)

            # Reset view
            view_ctl = self.vis.get_view_control()
            bounds = self.pcd.get_axis_aligned_bounding_box()
            view_ctl.set_lookat(bounds.get_center())
            view_ctl.set_zoom(0.8)

            self.vis.poll_events()
            self.vis.update_renderer()

        if img_new is not None:
            self.im.set_data(img_new)
            self.fig.canvas.draw()
            self.fig.canvas.flush_events()

    def run(self):
        self.update()
        while True:
            cmd = input("\n👉 Enter n (next), p (previous), number or q (quit): ").strip()
            if cmd == "n":
                self.index = (self.index + 1) % (self.max_index + 1)
            elif cmd == "p":
                self.index = (self.index - 1 + self.max_index + 1) % (self.max_index + 1)
            elif cmd.isdigit() and 0 <= int(cmd) <= self.max_index:
                self.index = int(cmd)
            elif cmd == "q":
                break
            else:
                print("❓ Invalid input, try again.")
            self.update()

        # Cleanup
        self.vis.destroy_window()
        plt.ioff()
        plt.close()

if __name__ == "__main__":
    max_index = get_max_index()
    print(f"📦 Found max index: {max_index}")
    viewer = Viewer(max_index)
    viewer.run()
